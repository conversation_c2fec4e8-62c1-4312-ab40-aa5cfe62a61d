#!/bin/bash
# 生产环境配置验证脚本
# 验证所有必要的文件和配置是否就绪

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 验证必要文件
validate_files() {
    log_info "验证必要文件..."
    
    local required_files=(
        ".env"
        "docker-compose.prod.yml"
        "backend/Dockerfile.prod"
        "frontend/Dockerfile.prod"
        "nginx/nginx.conf"
        "database/init/01_init.sql"
        "nginx/ssl/fullchain.pem"
        "nginx/ssl/privkey.pem"
    )
    
    local missing_files=()
    
    for file in "${required_files[@]}"; do
        if [ -f "$file" ]; then
            log_success "✓ $file"
        else
            log_error "✗ $file 缺失"
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -eq 0 ]; then
        log_success "所有必要文件都存在"
        return 0
    else
        log_error "缺少 ${#missing_files[@]} 个必要文件"
        return 1
    fi
}

# 验证环境变量
validate_env() {
    log_info "验证环境变量..."
    
    local required_vars=(
        "POSTGRES_USER"
        "POSTGRES_PASSWORD"
        "POSTGRES_DB"
        "SECRET_KEY"
        "DOMAIN"
        "SSL_DOMAIN"
    )
    
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if grep -q "^${var}=" .env && ! grep -q "^${var}=$" .env; then
            log_success "✓ $var"
        else
            log_error "✗ $var 未设置或为空"
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -eq 0 ]; then
        log_success "所有必要环境变量都已设置"
        return 0
    else
        log_error "缺少 ${#missing_vars[@]} 个必要环境变量"
        return 1
    fi
}

# 验证 Docker Compose 配置
validate_docker_compose() {
    log_info "验证 Docker Compose 配置..."
    
    if docker-compose -f docker-compose.prod.yml config > /dev/null 2>&1; then
        log_success "Docker Compose 配置有效"
        return 0
    else
        log_error "Docker Compose 配置无效"
        return 1
    fi
}

# 验证 SSL 证书
validate_ssl() {
    log_info "验证 SSL 证书..."
    
    if [ -f "nginx/ssl/fullchain.pem" ] && [ -f "nginx/ssl/privkey.pem" ]; then
        # 检查证书是否有效
        if openssl x509 -in nginx/ssl/fullchain.pem -noout -checkend 86400 > /dev/null 2>&1; then
            local expire_date=$(openssl x509 -in nginx/ssl/fullchain.pem -noout -enddate | cut -d= -f2)
            log_success "SSL 证书有效，到期时间: $expire_date"
            return 0
        else
            log_warning "SSL 证书将在24小时内到期或已过期"
            return 1
        fi
    else
        log_error "SSL 证书文件缺失"
        return 1
    fi
}

# 验证文件权限
validate_permissions() {
    log_info "验证文件权限..."
    
    # 检查脚本执行权限
    local scripts=(
        "scripts/deploy.sh"
        "scripts/backup_database.sh"
        "scripts/restore_database.sh"
        "scripts/verify_deployment.sh"
        "scripts/renew_xbai_ssl.sh"
    )
    
    for script in "${scripts[@]}"; do
        if [ -f "$script" ] && [ -x "$script" ]; then
            log_success "✓ $script 有执行权限"
        elif [ -f "$script" ]; then
            log_warning "⚠ $script 缺少执行权限"
            chmod +x "$script"
            log_info "已修复 $script 的执行权限"
        fi
    done
    
    # 检查 SSL 私钥权限
    if [ -f "nginx/ssl/privkey.pem" ]; then
        local perms=$(stat -c "%a" nginx/ssl/privkey.pem 2>/dev/null || stat -f "%A" nginx/ssl/privkey.pem 2>/dev/null)
        if [ "$perms" = "600" ]; then
            log_success "✓ SSL 私钥权限正确 (600)"
        else
            log_warning "⚠ SSL 私钥权限不正确，当前: $perms，应该: 600"
            chmod 600 nginx/ssl/privkey.pem
            log_info "已修复 SSL 私钥权限"
        fi
    fi
    
    log_success "文件权限验证完成"
}

# 主函数
main() {
    log_info "开始验证生产环境配置..."
    echo ""
    
    local errors=0
    
    validate_files || ((errors++))
    echo ""
    
    validate_env || ((errors++))
    echo ""
    
    validate_docker_compose || ((errors++))
    echo ""
    
    validate_ssl || ((errors++))
    echo ""
    
    validate_permissions
    echo ""
    
    if [ $errors -eq 0 ]; then
        log_success "🎉 生产环境配置验证通过！"
        echo ""
        echo "✅ 所有必要文件都存在"
        echo "✅ 环境变量配置正确"
        echo "✅ Docker Compose 配置有效"
        echo "✅ SSL 证书有效"
        echo "✅ 文件权限正确"
        echo ""
        echo "🚀 可以开始部署: ./scripts/deploy.sh"
    else
        log_error "❌ 发现 $errors 个问题，请修复后重新验证"
        exit 1
    fi
}

# 执行主函数
main "$@"
