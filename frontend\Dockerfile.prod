# 构建阶段
FROM node:18-alpine AS build

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV NODE_ENV=production \
    TZ=Asia/Shanghai

# 安装依赖
COPY package*.json ./
RUN npm config set registry https://registry.npmmirror.com && \
    npm install

# 将应用源代码复制到工作目录
COPY . .

# 构建应用
RUN ./node_modules/.bin/vite build

# 生产阶段
FROM nginx:alpine

# 复制构建产物到Nginx服务目录
COPY --from=build /app/dist /usr/share/nginx/html

# 复制Nginx配置文件（如果有自定义配置）
# COPY nginx.conf /etc/nginx/conf.d/default.conf

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD wget --spider http://localhost:80 || exit 1

# 启动Nginx
CMD ["nginx", "-g", "daemon off;"]
