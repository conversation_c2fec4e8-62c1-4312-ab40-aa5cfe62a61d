#!/bin/bash
# SSL 证书生成脚本
# 用于生成自签名证书（仅用于测试环境）

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 设置变量
SSL_DIR="nginx/ssl"
DOMAIN="${SSL_DOMAIN:-localhost}"
CERT_FILE="${SSL_DIR}/cert.pem"
KEY_FILE="${SSL_DIR}/private.key"
CSR_FILE="${SSL_DIR}/cert.csr"

# 创建 SSL 目录
log_info "创建 SSL 证书目录..."
mkdir -p ${SSL_DIR}

# 检查是否已存在证书
if [ -f "${CERT_FILE}" ] && [ -f "${KEY_FILE}" ]; then
    log_warning "SSL 证书已存在"
    read -p "是否要重新生成证书? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "保持现有证书"
        exit 0
    fi
fi

log_info "为域名 ${DOMAIN} 生成自签名 SSL 证书..."

# 生成私钥
log_info "生成私钥..."
openssl genrsa -out ${KEY_FILE} 2048

if [ $? -ne 0 ]; then
    log_error "私钥生成失败"
    exit 1
fi

# 生成证书签名请求 (CSR)
log_info "生成证书签名请求..."
openssl req -new -key ${KEY_FILE} -out ${CSR_FILE} -subj "/C=CN/ST=Beijing/L=Beijing/O=Sales Platform/OU=IT Department/CN=${DOMAIN}"

if [ $? -ne 0 ]; then
    log_error "CSR 生成失败"
    exit 1
fi

# 生成自签名证书
log_info "生成自签名证书..."
openssl x509 -req -days 365 -in ${CSR_FILE} -signkey ${KEY_FILE} -out ${CERT_FILE}

if [ $? -ne 0 ]; then
    log_error "证书生成失败"
    exit 1
fi

# 清理临时文件
rm -f ${CSR_FILE}

# 设置文件权限
chmod 600 ${KEY_FILE}
chmod 644 ${CERT_FILE}

log_success "SSL 证书生成完成！"
echo ""
echo "证书文件位置："
echo "  证书文件: ${CERT_FILE}"
echo "  私钥文件: ${KEY_FILE}"
echo ""
log_warning "注意: 这是自签名证书，仅适用于测试环境"
log_warning "生产环境请使用正式的 SSL 证书（如 Let's Encrypt）"
echo ""
echo "证书信息："
openssl x509 -in ${CERT_FILE} -text -noout | grep -E "(Subject:|Not Before|Not After)"

# 创建 nginx SSL 配置示例
log_info "创建 Nginx SSL 配置示例..."
cat > nginx/ssl_example.conf << 'EOF'
# SSL 配置示例
# 将此配置添加到您的 nginx.conf 中

server {
    listen 443 ssl http2;
    server_name localhost;

    # SSL 证书配置
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/private.key;

    # SSL 安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;

    # 其他配置...
    location / {
        root /usr/share/nginx/html;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    location /api/ {
        proxy_pass http://backend:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# HTTP 重定向到 HTTPS
server {
    listen 80;
    server_name localhost;
    return 301 https://$server_name$request_uri;
}
EOF

log_success "SSL 配置示例已创建: nginx/ssl_example.conf"
echo ""
echo "下一步："
echo "1. 检查生成的证书文件"
echo "2. 更新 nginx 配置以使用 SSL"
echo "3. 重启服务以应用 SSL 配置"
echo "4. 在浏览器中访问 https://localhost（需要接受自签名证书警告）"
