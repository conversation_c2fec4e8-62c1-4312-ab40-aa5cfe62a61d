#!/bin/bash
# Let's Encrypt SSL 证书自动获取脚本
# 用于生产环境获取免费的 SSL 证书

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -lt 2 ]; then
    echo "用法: $0 <域名> <邮箱>"
    echo "示例: $0 yourdomain.com <EMAIL>"
    exit 1
fi

DOMAIN=$1
EMAIL=$2
STAGING=${LETSENCRYPT_STAGING:-false}

# 设置变量
SSL_DIR="nginx/ssl"
WEBROOT_DIR="nginx/webroot"

log_info "开始为域名 ${DOMAIN} 获取 Let's Encrypt SSL 证书..."

# 检查域名是否可访问
log_info "检查域名 ${DOMAIN} 的可访问性..."
if ! ping -c 1 ${DOMAIN} > /dev/null 2>&1; then
    log_error "域名 ${DOMAIN} 无法访问，请检查 DNS 配置"
    exit 1
fi

# 创建必要的目录
log_info "创建必要的目录..."
mkdir -p ${SSL_DIR}
mkdir -p ${WEBROOT_DIR}

# 检查是否安装了 certbot
if ! command -v certbot &> /dev/null; then
    log_info "安装 certbot..."
    
    # 检测操作系统
    if [ -f /etc/debian_version ]; then
        # Debian/Ubuntu
        apt-get update
        apt-get install -y certbot
    elif [ -f /etc/redhat-release ]; then
        # CentOS/RHEL
        yum install -y epel-release
        yum install -y certbot
    else
        log_error "不支持的操作系统，请手动安装 certbot"
        exit 1
    fi
fi

# 停止现有的 nginx 服务（如果运行中）
log_info "停止现有的 nginx 服务..."
docker-compose -f docker-compose.prod.yml stop nginx 2>/dev/null || true

# 创建临时 nginx 配置用于验证
log_info "创建临时 nginx 配置..."
cat > nginx/temp_nginx.conf << EOF
events {
    worker_connections 1024;
}

http {
    server {
        listen 80;
        server_name ${DOMAIN};
        
        location /.well-known/acme-challenge/ {
            root ${WEBROOT_DIR};
        }
        
        location / {
            return 301 https://\$server_name\$request_uri;
        }
    }
}
EOF

# 启动临时 nginx 容器
log_info "启动临时 nginx 容器进行域名验证..."
docker run -d --name temp_nginx \
    -p 80:80 \
    -v $(pwd)/nginx/temp_nginx.conf:/etc/nginx/nginx.conf:ro \
    -v $(pwd)/${WEBROOT_DIR}:/usr/share/nginx/html \
    nginx:alpine

# 等待 nginx 启动
sleep 5

# 获取证书
log_info "获取 Let's Encrypt 证书..."

CERTBOT_ARGS="--webroot -w $(pwd)/${WEBROOT_DIR} -d ${DOMAIN} --email ${EMAIL} --agree-tos --non-interactive"

if [ "${STAGING}" = "true" ]; then
    log_warning "使用 Let's Encrypt 测试环境"
    CERTBOT_ARGS="${CERTBOT_ARGS} --staging"
fi

if certbot certonly ${CERTBOT_ARGS}; then
    log_success "证书获取成功！"
    
    # 复制证书到项目目录
    log_info "复制证书到项目目录..."
    cp /etc/letsencrypt/live/${DOMAIN}/fullchain.pem ${SSL_DIR}/cert.pem
    cp /etc/letsencrypt/live/${DOMAIN}/privkey.pem ${SSL_DIR}/private.key
    
    # 设置文件权限
    chmod 644 ${SSL_DIR}/cert.pem
    chmod 600 ${SSL_DIR}/private.key
    
    log_success "证书已复制到 ${SSL_DIR}/"
else
    log_error "证书获取失败"
    
    # 清理临时容器
    docker stop temp_nginx 2>/dev/null || true
    docker rm temp_nginx 2>/dev/null || true
    
    exit 1
fi

# 清理临时容器
log_info "清理临时容器..."
docker stop temp_nginx 2>/dev/null || true
docker rm temp_nginx 2>/dev/null || true

# 创建证书自动续期脚本
log_info "创建证书自动续期脚本..."
cat > scripts/renew_ssl_cert.sh << 'EOF'
#!/bin/bash
# SSL 证书自动续期脚本

# 续期证书
certbot renew --quiet

# 如果证书更新了，重新复制到项目目录
if [ $? -eq 0 ]; then
    cp /etc/letsencrypt/live/*/fullchain.pem nginx/ssl/cert.pem
    cp /etc/letsencrypt/live/*/privkey.pem nginx/ssl/private.key
    
    # 重启 nginx 容器
    docker-compose -f docker-compose.prod.yml restart nginx
    
    echo "SSL 证书已更新并重启服务"
fi
EOF

chmod +x scripts/renew_ssl_cert.sh

# 添加到 crontab（每月检查一次）
log_info "设置自动续期任务..."
(crontab -l 2>/dev/null; echo "0 3 1 * * $(pwd)/scripts/renew_ssl_cert.sh") | crontab -

# 更新 .env 文件
log_info "更新 .env 文件..."
sed -i "s/SSL_DOMAIN=.*/SSL_DOMAIN=${DOMAIN}/" .env
sed -i "s/FORCE_HTTPS=.*/FORCE_HTTPS=true/" .env
sed -i "s/LETSENCRYPT_EMAIL=.*/LETSENCRYPT_EMAIL=${EMAIL}/" .env

log_success "Let's Encrypt SSL 证书设置完成！"
echo ""
echo "证书文件位置："
echo "  证书文件: ${SSL_DIR}/cert.pem"
echo "  私钥文件: ${SSL_DIR}/private.key"
echo ""
echo "证书信息："
openssl x509 -in ${SSL_DIR}/cert.pem -text -noout | grep -E "(Subject:|Not Before|Not After)"
echo ""
echo "下一步："
echo "1. 更新 nginx 配置以启用 SSL"
echo "2. 重启服务: docker-compose -f docker-compose.prod.yml up -d"
echo "3. 访问 https://${DOMAIN} 验证证书"
echo ""
log_info "证书将在到期前自动续期（每月检查一次）"
