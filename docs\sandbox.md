# 支付宝沙箱集成完整指南

## 📋 **概述**

本文档是支付宝沙箱环境的完整集成指南，包含沙箱账号信息、配置标准、集成步骤和测试方法。项目已成功集成支付宝沙箱环境，支持真实的支付流程测试。

## 🔧 **当前生效配置**

### 统一配置标准
```yaml
# Docker环境变量 (docker-compose.yml)
ALIPAY_APP_ID: "2021000148685433"
ALIPAY_PRIVATE_KEY_PATH: "/app/keys/app_private_key.pem"
ALIPAY_PUBLIC_KEY_PATH: "/app/keys/alipay_public_key.pem"
ALIPAY_NOTIFY_URL: "http://localhost:8000/api/v1/payments/notify"
ALIPAY_RETURN_URL: "http://localhost:5173/payment-result"
```

### 网关地址
- **沙箱环境**: `https://openapi-sandbox.dl.alipaydev.com/gateway.do`
- **生产环境**: `https://openapi.alipay.com/gateway.do`

### 签名算法
- **sign_type**: RSA2

### 密钥格式
- **私钥格式**: PKCS1 (-----BEGIN RSA PRIVATE KEY-----)
- **公钥格式**: X.509 (-----BEGIN PUBLIC KEY-----)

## 👥 **沙箱账号信息**

### 商家信息
- 商户账号: <EMAIL>
- 登录密码: 111111
- 商户PID: 2088721067958003
- 账户余额: 1000000.00

### 买家信息
- 买家账号: <EMAIL>
- 登录密码: 111111
- 支付密码: 111111
- 用户UID: 2088722067958011
- 用户名称: kcyghi1984
- 证件类型: IDENTITY_CARD
- 证件账号: 984394199209142391
- 账户余额: 1000000.00

## 🌐 **沙箱环境特点**

1. **完全隔离**：在沙箱环境中的操作不会影响生产环境的数据
2. **低门槛**：无需等待产品签约，可以直接调用接口进行测试
3. **独立数据体系**：沙箱环境的数据（如用户ID等）与生产环境完全独立
4. **虚拟交易**：所有交易都是虚拟的，不涉及真实资金流动

## 🔌 **支持的接口**

| 接口类型 | 接口中文名 | 接口英文名称 | 沙箱环境支持情况 |
|---------|-----------|------------|---------------|
| 电脑网站支付 | 统一收单下单并支付页面接口 | alipay.trade.page.pay | ✅ 支持 |
| 辅助接口 | 统一收单交易查询 | alipay.trade.query | ✅ 支持 |
| 辅助接口 | 统一收单交易退款接口 | alipay.trade.refund | ✅ 支持 |
| 辅助接口 | 统一收单交易退款查询接口 | alipay.trade.fastpay.refund.query | ✅ 支持 |
| 辅助接口 | 统一收单交易关闭接口 | alipay.trade.close | ✅ 支持 |
| 辅助接口 | 查询对账单下载地址 | alipay.data.dataservice.bill.downloadurl.query | ⚠️ 支持（模拟调用） |

## 🚀 **集成步骤**

### 1. 申请支付宝开发者账号
1. 访问[支付宝开放平台](https://open.alipay.com/)，注册开发者账号
2. 创建应用，选择"自研开发者应用"
3. 在应用详情页面，找到"沙箱环境"，开通沙箱测试功能
4. 获取沙箱环境的应用信息：APPID、支付宝公钥、应用私钥

### 2. 生成RSA密钥对
```bash
# 生成私钥
openssl genrsa -out app_private_key.pem 2048

# 生成公钥
openssl rsa -in app_private_key.pem -pubout -out app_public_key.pem

# 注意：项目使用PKCS1格式，无需转换为PKCS8
```

### 3. 安装Python Alipay SDK
```bash
docker exec -it sales_platform_backend pip install python-alipay-sdk --upgrade
```

### 4. 配置密钥文件
```bash
# 在后端容器中创建密钥目录
docker exec -it sales_platform_backend mkdir -p /app/keys

# 将密钥文件复制到容器中
docker cp app_private_key.pem sales_platform_backend:/app/keys/
docker cp alipay_public_key.pem sales_platform_backend:/app/keys/
```

## ⚠️ **常见错误及解决方案**

### 1. invalid-signature-type-said-interface
- **原因**: 使用了不支持的签名算法 (如RSA1)
- **解决**: 确保 `sign_type` 设置为 `RSA2`
- **状态**: ✅ 已修复

### 2. invalid-signature (验签出错)
- **原因**: 虽然设置了RSA2，但实际签名使用了错误的算法或格式
- **解决**: 使用支付宝SDK生成正确的RSA2签名，不要手动构造签名
- **状态**: ✅ 已修复

### 3. RSA key format is not supported
- **原因**: 私钥文件缺少PEM格式头部
- **解决**: 确保私钥文件包含 `-----BEGIN RSA PRIVATE KEY-----` 头部
- **状态**: ✅ 已修复

### 4. APP_ID不匹配
- **原因**: 不同文档中使用了不同的APP_ID
- **解决**: 统一使用 `2021000148685433`
- **状态**: ✅ 已修复

### 5. 前端测试页面签名错误
- **原因**: 前端无法生成有效的RSA2签名
- **解决**: 通过后端API生成支付链接，不要在前端直接构造
- **状态**: ✅ 已修复

## 🧪 **测试方法**

### 1. 通过前端应用测试（推荐）
```
1. 访问 http://localhost:5173
2. 登录系统（testuser / password123）
3. 浏览产品并创建订单
4. 点击支付按钮
5. 使用沙箱账户完成支付
```

### 2. 通过API直接测试
```bash
# 生成测试支付链接
docker exec sales_platform_backend python -c "
from app.services.payment import PaymentService
import time
ps = PaymentService()
order_string = ps.alipay_client.api_alipay_trade_page_pay(
    out_trade_no='TEST_ORDER_' + str(int(time.time())),
    total_amount='0.01',
    subject='测试商品',
    return_url='http://localhost:5173/payment-result',
    notify_url='http://localhost:8000/api/v1/payments/notify'
)
print(f'https://openapi-sandbox.dl.alipaydev.com/gateway.do?{order_string}')
"
```

### 3. 配置验证清单
- [ ] APP_ID: `2021000148685433`
- [ ] 私钥路径: `/app/keys/app_private_key.pem`
- [ ] 公钥路径: `/app/keys/alipay_public_key.pem`
- [ ] 签名算法: `RSA2`
- [ ] 网关地址: `https://openapi-sandbox.dl.alipaydev.com/gateway.do`
- [ ] 私钥格式: PKCS1 (包含PEM头部)
- [ ] 公钥格式: X.509 (包含PEM头部)

## 🔄 **完整支付流程**

集成支付宝后，完整的支付流程如下：

1. **用户创建订单** - 用户在前端选择商品并创建订单
2. **点击支付按钮** - 用户确认订单信息并点击支付
3. **生成支付链接** - 系统生成支付宝支付链接，将用户重定向到支付宝
4. **支付宝支付** - 用户在支付宝完成支付
5. **同步通知** - 支付宝将用户重定向回您的网站
6. **异步通知** - 支付宝向您的服务器发送异步通知
7. **更新订单状态** - 系统处理异步通知，更新订单状态
8. **生成许可密钥** - 如果是软件产品，系统生成许可密钥
9. **查看结果** - 用户可以查看订单状态和许可密钥

## 🏭 **生产环境部署注意事项**

### 1. 申请正式应用
- 完成企业认证，并通过支付宝的审核
- 获取正式的APPID和密钥

### 2. 更换网关地址
```yaml
# 从沙箱环境
ALIPAY_GATEWAY: "https://openapi-sandbox.dl.alipaydev.com/gateway.do"

# 更改为生产环境
ALIPAY_GATEWAY: "https://openapi.alipay.com/gateway.do"
```

### 3. 配置真实回调地址
```yaml
# 确保使用真实域名
ALIPAY_NOTIFY_URL: "https://yourdomain.com/api/v1/payments/notify"
ALIPAY_RETURN_URL: "https://yourdomain.com/payment-result"
```

### 4. 安全存储密钥
- 在生产环境中，应使用更安全的方式存储密钥
- 考虑使用密钥管理服务或环境变量

### 5. 实现完整的日志记录
- 记录所有支付相关的操作，便于排查问题
- 添加监控和告警，对支付流程进行监控

## 📝 **注意事项**

1. **安全性**：确保私钥文件安全，不要将其提交到代码仓库
2. **沙箱与生产环境**：沙箱和生产环境使用不同的网关地址，上线前需要修改
3. **异步通知**：生产环境中必须正确处理异步通知，这是支付状态的可靠来源
4. **订单超时**：考虑实现订单超时取消机制
5. **退款流程**：如需支持退款，还需实现退款相关接口
6. **沙箱限制**：
   - 沙箱环境并非100%与生产环境一致
   - 不支持银行卡支付
   - 无法模拟测试收单退款冲退完成通知接口
   - 所有交易都是虚拟的，不涉及真实资金流动

## 📚 **相关文档**

- [数据库管理文档](./database_management.md)
- [许可密钥管理修复汇总](./issue/license_key_management_fixes.md)
- [部署检查清单](./deployment_checklist.md)

---

**最后更新**: 2024年12月
**维护者**: 开发团队
**状态**: 已集成并测试通过
