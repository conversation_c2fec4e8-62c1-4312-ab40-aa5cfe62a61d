#!/bin/bash
# 为 xbai.fans 域名获取 Let's Encrypt SSL 证书的专用脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 设置变量
PRIMARY_DOMAIN="xbai.fans"
WWW_DOMAIN="www.xbai.fans"
EMAIL="<EMAIL>"
SSL_DIR="nginx/ssl"
WEBROOT_DIR="nginx/webroot"

log_info "开始为 ${PRIMARY_DOMAIN} 和 ${WWW_DOMAIN} 获取 Let's Encrypt SSL 证书..."

# 检查域名解析
log_info "检查域名解析..."
for domain in $PRIMARY_DOMAIN $WWW_DOMAIN; do
    if ! nslookup $domain > /dev/null 2>&1; then
        log_error "域名 $domain 解析失败，请检查 DNS 配置"
        exit 1
    else
        log_success "域名 $domain 解析正常"
    fi
done

# 创建必要的目录
log_info "创建必要的目录..."
mkdir -p ${SSL_DIR}
mkdir -p ${WEBROOT_DIR}

# 检查是否安装了 certbot
if ! command -v certbot &> /dev/null; then
    log_info "安装 certbot..."
    
    # 检测操作系统并安装 certbot
    if [ -f /etc/debian_version ]; then
        # Debian/Ubuntu
        apt-get update
        apt-get install -y certbot
    elif [ -f /etc/redhat-release ]; then
        # CentOS/RHEL
        yum install -y epel-release
        yum install -y certbot
    else
        log_error "不支持的操作系统，请手动安装 certbot"
        exit 1
    fi
fi

# 停止现有的服务
log_info "停止现有的服务..."
docker-compose -f docker-compose.prod.yml down 2>/dev/null || true

# 创建临时 nginx 配置
log_info "创建临时 nginx 配置..."
cat > nginx/temp_nginx.conf << EOF
events {
    worker_connections 1024;
}

http {
    server {
        listen 80;
        server_name ${PRIMARY_DOMAIN} ${WWW_DOMAIN};
        
        location /.well-known/acme-challenge/ {
            root /usr/share/nginx/html;
        }
        
        location / {
            return 200 'SSL Certificate Setup in Progress';
            add_header Content-Type text/plain;
        }
    }
}
EOF

# 启动临时 nginx 容器
log_info "启动临时 nginx 容器..."
docker run -d --name temp_nginx_ssl \
    -p 80:80 \
    -v $(pwd)/nginx/temp_nginx.conf:/etc/nginx/nginx.conf:ro \
    -v $(pwd)/${WEBROOT_DIR}:/usr/share/nginx/html \
    nginx:alpine

# 等待 nginx 启动
sleep 5

# 获取证书（包含主域名和 www 子域名）
log_info "获取 Let's Encrypt 证书..."
if certbot certonly \
    --webroot \
    -w $(pwd)/${WEBROOT_DIR} \
    -d ${PRIMARY_DOMAIN} \
    -d ${WWW_DOMAIN} \
    --email ${EMAIL} \
    --agree-tos \
    --non-interactive; then
    
    log_success "证书获取成功！"
    
    # 复制证书到项目目录
    log_info "复制证书到项目目录..."
    cp /etc/letsencrypt/live/${PRIMARY_DOMAIN}/fullchain.pem ${SSL_DIR}/cert.pem
    cp /etc/letsencrypt/live/${PRIMARY_DOMAIN}/privkey.pem ${SSL_DIR}/private.key
    
    # 设置文件权限
    chmod 644 ${SSL_DIR}/cert.pem
    chmod 600 ${SSL_DIR}/private.key
    
    log_success "证书已复制到 ${SSL_DIR}/"
    
    # 更新 .env 文件
    log_info "更新 .env 文件..."
    sed -i "s/SSL_CERT_TYPE=.*/SSL_CERT_TYPE=letsencrypt/" .env
    
else
    log_error "证书获取失败"
    
    # 清理临时容器
    docker stop temp_nginx_ssl 2>/dev/null || true
    docker rm temp_nginx_ssl 2>/dev/null || true
    
    exit 1
fi

# 清理临时容器
log_info "清理临时容器..."
docker stop temp_nginx_ssl 2>/dev/null || true
docker rm temp_nginx_ssl 2>/dev/null || true
rm -f nginx/temp_nginx.conf

# 创建证书自动续期脚本
log_info "创建证书自动续期脚本..."
cat > scripts/renew_xbai_ssl.sh << 'EOF'
#!/bin/bash
# xbai.fans SSL 证书自动续期脚本

# 续期证书
certbot renew --quiet

# 如果证书更新了，重新复制到项目目录
if [ $? -eq 0 ]; then
    cp /etc/letsencrypt/live/xbai.fans/fullchain.pem nginx/ssl/cert.pem
    cp /etc/letsencrypt/live/xbai.fans/privkey.pem nginx/ssl/private.key
    
    # 重启 nginx 容器
    docker-compose -f docker-compose.prod.yml restart nginx
    
    echo "$(date): SSL 证书已更新并重启服务" >> logs/ssl_renewal.log
fi
EOF

chmod +x scripts/renew_xbai_ssl.sh

# 添加到 crontab（每月1号凌晨3点检查）
log_info "设置自动续期任务..."
(crontab -l 2>/dev/null | grep -v "renew_xbai_ssl"; echo "0 3 1 * * $(pwd)/scripts/renew_xbai_ssl.sh") | crontab -

# 创建 nginx SSL 配置
log_info "创建 nginx SSL 配置..."
cat > nginx/ssl_config.conf << EOF
# SSL 配置 for xbai.fans
server {
    listen 443 ssl http2;
    server_name ${PRIMARY_DOMAIN} ${WWW_DOMAIN};

    # SSL 证书配置
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/private.key;

    # SSL 安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;

    # 前端静态文件
    location / {
        root /usr/share/nginx/html;
        index index.html;
        try_files \$uri \$uri/ /index.html;
    }

    # API 代理
    location /api/ {
        proxy_pass http://backend:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # 健康检查
    location /health {
        proxy_pass http://backend:8000/health;
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}

# HTTP 重定向到 HTTPS
server {
    listen 80;
    server_name ${PRIMARY_DOMAIN} ${WWW_DOMAIN};
    return 301 https://\$server_name\$request_uri;
}
EOF

log_success "Let's Encrypt SSL 证书设置完成！"
echo ""
echo "证书信息："
openssl x509 -in ${SSL_DIR}/cert.pem -text -noout | grep -E "(Subject:|DNS:|Not Before|Not After)"
echo ""
echo "证书文件位置："
echo "  证书文件: ${SSL_DIR}/cert.pem"
echo "  私钥文件: ${SSL_DIR}/private.key"
echo "  Nginx 配置: nginx/ssl_config.conf"
echo ""
echo "下一步："
echo "1. 更新 nginx 配置以使用 SSL（参考 nginx/ssl_config.conf）"
echo "2. 重启服务: docker-compose -f docker-compose.prod.yml up -d"
echo "3. 访问 https://xbai.fans 验证证书"
echo "4. 访问 https://www.xbai.fans 验证证书"
echo ""
log_info "证书将在到期前自动续期（每月1号检查）"
