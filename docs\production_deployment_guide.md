# 生产环境部署指南

## 概述

本指南适用于全新的生产环境部署，无需数据迁移。所有必要的数据库结构、约束和索引都已集成到初始化脚本中。

## ✅ 代码和数据库一致性验证

### 已验证的一致性
- **数据库表结构** ✅ 与后端模型完全一致
- **字段类型** ✅ 所有金额字段使用 `DECIMAL(10, 2)` 类型
- **约束条件** ✅ 添加了完整的数据完整性约束
- **索引优化** ✅ 为所有常用查询字段添加了索引
- **API接口** ✅ 前后端接口调用完全匹配

### 数据库优化特性
```sql
-- 数据完整性约束
CHECK (product_type IN ('software', 'hardware'))
CHECK (status IN ('pending', 'paid', 'cancelled'))
CHECK (price > 0)

-- 性能优化索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_products_name ON products(name);
CREATE INDEX idx_orders_user_id ON orders(user_id);
-- ... 更多索引
```

## 🚀 快速部署步骤

### 1. 环境准备
```bash
# 确保已安装 Docker 和 Docker Compose
docker --version
docker-compose --version

# 克隆项目代码
git clone <your-repository>
cd sales-platform
```

### 2. 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑 .env 文件，重点配置以下项目：
# - SECRET_KEY: 生成强随机密钥
# - POSTGRES_PASSWORD: 设置强数据库密码
# - ALIPAY_APP_ID: 支付宝应用ID
# - FIRST_SUPERUSER_PASSWORD: 管理员密码
```

### 3. 准备密钥文件
```bash
# 将生产环境的支付宝密钥文件放入指定目录
cp your-production-keys/* backend/keys/

# 设置正确的文件权限
chmod 600 backend/keys/*.pem
```

### 4. 运行修复脚本（可选）
```bash
# 运行自动修复脚本，检查配置完整性
chmod +x scripts/fix_production_issues.sh
./scripts/fix_production_issues.sh
```

### 5. 执行部署
```bash
# 运行部署脚本
chmod +x scripts/deploy.sh
./scripts/deploy.sh
```

## 📋 部署验证清单

### 服务状态检查
```bash
# 检查所有容器状态
docker-compose -f docker-compose.prod.yml ps

# 检查服务日志
docker-compose -f docker-compose.prod.yml logs -f
```

### 功能验证
- [ ] 前端页面正常访问 (http://localhost)
- [ ] 后端API正常响应 (http://localhost/api/v1/health)
- [ ] 用户注册/登录功能正常
- [ ] 产品浏览功能正常
- [ ] 管理员登录正常 (admin/your-password)

### 数据库验证
```bash
# 检查数据库连接
docker exec sales_platform_database pg_isready -U user -d sales_platform

# 检查表结构
docker exec sales_platform_database psql -U user -d sales_platform -c "\dt"

# 检查索引
docker exec sales_platform_database psql -U user -d sales_platform -c "\di"
```

## 🔧 常见问题排查

### 1. 容器启动失败
```bash
# 查看详细错误日志
docker-compose -f docker-compose.prod.yml logs service-name

# 检查配置文件语法
docker-compose -f docker-compose.prod.yml config
```

### 2. 数据库连接失败
```bash
# 检查数据库容器状态
docker exec sales_platform_database pg_isready

# 检查环境变量配置
echo $DATABASE_URL
```

### 3. 前端无法访问后端
```bash
# 检查网络连接
docker network ls
docker network inspect sales_platform_network

# 检查端口映射
docker port sales_platform_nginx
```

## 🔒 安全配置建议

### 立即配置
1. **更改默认密码**
   - 数据库密码
   - 管理员密码
   - JWT密钥

2. **启用HTTPS**
   - 获取SSL证书
   - 配置Nginx HTTPS
   - 强制HTTPS重定向

3. **网络安全**
   - 配置防火墙
   - 限制数据库访问
   - 启用API限流

### 推荐配置
1. **监控和日志**
   - 启用应用监控
   - 配置错误告警
   - 设置日志轮转

2. **备份策略**
   - 自动数据库备份
   - 配置文件备份
   - 定期备份验证

## 📊 性能优化

### 数据库优化
- ✅ 已添加必要索引
- ✅ 已配置连接池
- 建议：定期分析查询性能

### 应用优化
- ✅ 已启用Gzip压缩
- ✅ 已配置静态文件缓存
- 建议：配置CDN加速

### 容器优化
- ✅ 已使用多阶段构建
- ✅ 已配置健康检查
- 建议：根据负载调整资源限制

## 🔄 维护操作

### 日常维护
```bash
# 查看服务状态
docker-compose -f docker-compose.prod.yml ps

# 查看资源使用
docker stats

# 备份数据库
./scripts/backup_database.sh

# 查看日志
docker-compose -f docker-compose.prod.yml logs --tail 100
```

### 更新部署
```bash
# 拉取最新代码
git pull origin main

# 重新部署
./scripts/deploy.sh
```

### 回滚操作
```bash
# 停止当前服务
docker-compose -f docker-compose.prod.yml down

# 恢复数据库备份
./scripts/restore_database.sh backup-file.sql

# 启动之前的版本
docker-compose -f docker-compose.prod.yml up -d
```

## 📞 技术支持

如果在部署过程中遇到问题：

1. 检查本文档的常见问题排查部分
2. 查看详细的错误日志
3. 验证配置文件的正确性
4. 确保所有依赖服务正常运行

## 🎯 部署成功标志

当看到以下内容时，说明部署成功：

1. 所有Docker容器状态为"Up"
2. 前端页面正常显示
3. API健康检查返回200状态
4. 数据库连接正常
5. 管理员可以正常登录

恭喜！您的软件和设备销售平台已成功部署到生产环境！
