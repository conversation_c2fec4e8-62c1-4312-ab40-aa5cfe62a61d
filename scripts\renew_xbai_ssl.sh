#!/bin/bash
# xbai.fans SSL 证书自动续期脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 设置变量
DOMAIN="xbai.fans"
SSL_DIR="nginx/ssl"
LOG_FILE="logs/ssl_renewal.log"
CERT_FILE="${SSL_DIR}/fullchain.pem"
KEY_FILE="${SSL_DIR}/privkey.pem"

# 创建日志目录
mkdir -p logs

# 记录开始时间
echo "$(date): 开始检查 SSL 证书续期" >> ${LOG_FILE}

# 检查证书是否存在
if [ ! -f "${CERT_FILE}" ]; then
    log_error "证书文件不存在: ${CERT_FILE}"
    echo "$(date): 错误 - 证书文件不存在" >> ${LOG_FILE}
    exit 1
fi

# 检查证书到期时间
EXPIRE_DATE=$(openssl x509 -in ${CERT_FILE} -noout -enddate | cut -d= -f2)
EXPIRE_TIMESTAMP=$(date -d "${EXPIRE_DATE}" +%s)
CURRENT_TIMESTAMP=$(date +%s)
DAYS_UNTIL_EXPIRE=$(( (EXPIRE_TIMESTAMP - CURRENT_TIMESTAMP) / 86400 ))

log_info "证书到期时间: ${EXPIRE_DATE}"
log_info "距离到期还有: ${DAYS_UNTIL_EXPIRE} 天"

# 如果证书在30天内到期，则尝试续期
if [ ${DAYS_UNTIL_EXPIRE} -le 30 ]; then
    log_warning "证书将在 ${DAYS_UNTIL_EXPIRE} 天内到期，开始续期..."
    echo "$(date): 证书将在 ${DAYS_UNTIL_EXPIRE} 天内到期，开始续期" >> ${LOG_FILE}
    
    # 备份当前证书
    log_info "备份当前证书..."
    cp ${CERT_FILE} ${CERT_FILE}.backup.$(date +%Y%m%d)
    cp ${KEY_FILE} ${KEY_FILE}.backup.$(date +%Y%m%d)
    
    # 续期证书
    log_info "执行证书续期..."
    if certbot renew --quiet --cert-name ${DOMAIN}; then
        log_success "证书续期成功"
        
        # 检查 Let's Encrypt 证书位置
        if [ -f "/etc/letsencrypt/live/${DOMAIN}/fullchain.pem" ]; then
            # 复制新证书到项目目录
            log_info "复制新证书到项目目录..."
            cp /etc/letsencrypt/live/${DOMAIN}/fullchain.pem ${CERT_FILE}
            cp /etc/letsencrypt/live/${DOMAIN}/privkey.pem ${KEY_FILE}
            
            # 设置文件权限
            chmod 644 ${CERT_FILE}
            chmod 600 ${KEY_FILE}
            
            log_success "证书文件已更新"
            
            # 重启 nginx 容器
            log_info "重启 nginx 容器..."
            if docker-compose -f docker-compose.prod.yml restart nginx; then
                log_success "nginx 容器重启成功"
                echo "$(date): SSL 证书已更新并重启服务" >> ${LOG_FILE}
                
                # 验证新证书
                log_info "验证新证书..."
                NEW_EXPIRE_DATE=$(openssl x509 -in ${CERT_FILE} -noout -enddate | cut -d= -f2)
                log_success "新证书到期时间: ${NEW_EXPIRE_DATE}"
                echo "$(date): 新证书到期时间: ${NEW_EXPIRE_DATE}" >> ${LOG_FILE}
                
                # 发送通知（如果配置了邮件）
                if command -v mail &> /dev/null; then
                    echo "SSL 证书已成功续期。新证书到期时间: ${NEW_EXPIRE_DATE}" | mail -s "SSL 证书续期成功 - xbai.fans" <EMAIL>
                fi
                
            else
                log_error "nginx 容器重启失败"
                echo "$(date): 错误 - nginx 容器重启失败" >> ${LOG_FILE}
                exit 1
            fi
        else
            log_error "Let's Encrypt 证书文件不存在"
            echo "$(date): 错误 - Let's Encrypt 证书文件不存在" >> ${LOG_FILE}
            exit 1
        fi
    else
        log_error "证书续期失败"
        echo "$(date): 错误 - 证书续期失败" >> ${LOG_FILE}
        
        # 恢复备份证书
        log_info "恢复备份证书..."
        cp ${CERT_FILE}.backup.$(date +%Y%m%d) ${CERT_FILE}
        cp ${KEY_FILE}.backup.$(date +%Y%m%d) ${KEY_FILE}
        
        exit 1
    fi
else
    log_info "证书还有 ${DAYS_UNTIL_EXPIRE} 天到期，无需续期"
    echo "$(date): 证书还有 ${DAYS_UNTIL_EXPIRE} 天到期，无需续期" >> ${LOG_FILE}
fi

# 清理旧的备份文件（保留最近7天的备份）
log_info "清理旧的备份文件..."
find ${SSL_DIR} -name "*.backup.*" -type f -mtime +7 -delete

echo "$(date): SSL 证书检查完成" >> ${LOG_FILE}
log_success "SSL 证书检查完成"
