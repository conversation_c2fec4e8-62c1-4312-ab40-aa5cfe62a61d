#!/bin/bash
# 数据库恢复脚本

# 检查参数
if [ $# -ne 1 ]; then
    echo "用法: $0 <备份文件路径>"
    echo "示例: $0 ./backups/db_backup_20250513_120000.sql"
    exit 1
fi

BACKUP_FILE=$1

# 自动检测数据库容器名称
CONTAINER_NAME=$(docker-compose -f docker-compose.prod.yml ps -q database 2>/dev/null)
if [ -z "$CONTAINER_NAME" ]; then
    # 如果找不到，尝试通过服务名查找
    CONTAINER_NAME=$(docker-compose -f docker-compose.prod.yml ps database 2>/dev/null | grep -v "Name" | awk '{print $1}' | head -1)
fi

if [ -z "$CONTAINER_NAME" ]; then
    echo "错误: 无法找到数据库容器"
    echo "请确保 docker-compose.prod.yml 中的数据库服务正在运行"
    exit 1
fi

echo "使用数据库容器: $CONTAINER_NAME"

# 检查备份文件是否存在
if [ ! -f ${BACKUP_FILE} ]; then
    echo "错误: 备份文件 ${BACKUP_FILE} 不存在"
    exit 1
fi

# 检查文件是否为压缩文件
if [[ ${BACKUP_FILE} == *.gz ]]; then
    echo "检测到压缩文件，正在解压..."
    gunzip -c ${BACKUP_FILE} > ${BACKUP_FILE%.gz}
    BACKUP_FILE=${BACKUP_FILE%.gz}
    echo "解压完成: ${BACKUP_FILE}"
fi

# 执行恢复
echo "警告: 此操作将覆盖当前数据库内容"
read -p "是否继续? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "操作已取消"
    exit 0
fi

echo "开始恢复数据库..."
cat ${BACKUP_FILE} | docker exec -i ${CONTAINER_NAME} psql -U user -d sales_platform

# 检查恢复是否成功
if [ $? -eq 0 ]; then
    echo "数据库恢复成功"
else
    echo "数据库恢复失败"
    exit 1
fi

echo "恢复过程完成"
