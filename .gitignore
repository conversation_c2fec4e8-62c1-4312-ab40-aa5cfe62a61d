# 文件路径: .gitignore

# 忽略 Python 相关的目录和文件
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
.ipynb_checkpoints
.pytest_cache/

# 忽略 Node.js 相关的目录和文件
node_modules/
dist/
build/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.parcel-cache/

# 忽略 Docker 相关的目录和文件
*.swp
*.swo

# 忽略数据库文件 (如果不是容器化管理)
*.sqlite3
*.db

# 忽略环境变量文件
.env
.env.*
!.env.example # 不忽略 .env.example 文件

# 忽略日志文件
logs/
*.log

# 忽略操作系统相关文件
.DS_Store
Thumbs.db

# VSCode 忽略文件
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# 其他临时文件
*~
node_modules/
.roo/
.roomodes
backend/keys
nginx\ssl